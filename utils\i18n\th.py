# -*- coding: utf-8 -*-
"""
Error messages in Thai language
"""

ERROR_MESSAGES = {
    # Authentication errors (1000-1099)
    1000: "ไม่พบข้อมูลผู้ใช้",
    1001: "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง",
    1002: "กรอกรหัสผ่านไม่ถูกต้องเกินจำนวน 3 ครั้ง กรุณารอ 30 วินาที เพื่อกรอกรหัสผ่านอีกครั้ง หรือกดลืมรหัสผ่าน เพื่อตั้งค่ารหัสผ่านใหม่",
    1003: "บัญชีผู้ใช้ยังไม่ได้ยืนยัน",
    1004: "ชื่อผู้ใช้งานโดนล็อค",
    1005: "โทเคนไม่ถูกต้อง",
    1006: "ไม่มีสิทธิ์เข้าถึง",
    1007: "รหัสผ่านไม่ถูกต้อง",
    1008: "ท่านยังไม่ได้ยื่นเรื่องขึ้นทะเบียนที่ปรึกษา",
    1010: "บัญชีนี้ถูกลบแล้ว",
    1011: "ท่านยังขึ้นทะเบียนที่ปรึกษาไม่สำเร็จ ไม่สามารถเข้าสู่ระบบในฐานะที่ปรึกษาได้",

    # Validation errors (2000-2099)
    2000: "ข้อมูลที่ส่งมาไม่ถูกต้อง",
    2001: "ฟิลด์จำเป็นไม่ได้กรอก",
    2002: "รูปแบบอีเมลไม่ถูกต้อง",
    2003: "รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง",
    2004: "รูปแบบข้อมูลไม่ถูกต้อง",
    2005: "ขนาดไฟล์เกินกำหนด",
    2006: "ประเภทไฟล์ไม่รองรับ",
    2007: "ข้อมูลซ้ำ",
    2008: "ค่าไม่อยู่ในช่วงที่กำหนด",
    2009: "พารามิเตอร์ไม่ถูกต้อง",
    2010: "ข้อมูลนี้มีอยู่ในระบบแล้ว",
    2011: "อีเมลนี้มีในระบบแล้ว",
    2012: "เบอร์โทรศัพท์นี้มีในระบบแล้ว",
    2013: "ชื่อผู้ใช้งานนี้มีในระบบแล้ว",
    2014: "ชื่อผู้ใช้งานไม่ถูกต้อง",
    2015: "ชื่อผู้ใช้งานต้องมีความยาวอย่างน้อย 4 ตัวอักษร",
    2016: "รหัสผ่านและการยืนยันรหัสผ่านไม่ตรงกัน",
    2017: "ชื่อผู้ใช้สามารถใช้ได้เฉพาะตัวอักษร ตัวเลข และสัญลักษณ์ @ _ - . เท่านั้น",
    2018: "เบอร์โทรศัพท์ต้องเป็นตัวเลข 10 หลักเท่านั้น",
    2019: "หมายเลขบัตรประชาชนนี้มีในระบบแล้ว",
    2020: "OTP ถูกยืนยันแล้ว",
    2021: "รหัสยืนยันตัวตนหมดอายุ",
    2022: "OTP ได้ถูกใช้แล้ว",
    2023: "รหัสยืนยันตัวตนไม่ถูกต้อง",
    2024: "OTP token หมดอายุ",
    2025: "OTP token ไม่ถูกต้อง",
    2026: "OTP token ไม่ถูกยืนยัน",
    2027: "ไม่สามารถส่งอีเมลได้ กรุณาลองใหม่อีกครั้ง",
    2028: "การยืนยัน OTP ล้มเหลว",
    2029: "อีเมลที่ลงทะเบียนต้องตรงกับอีเมลที่ใช้ในการ verify OTP",
    2030: "เลขบัตรประชาชนต้องมี 13 หลัก",
    2031: "เลขบัตรประชาชนต้องเป็นตัวเลขเท่านั้น",
    2032: "กรุณาระบุ Action Log",
    2033: "กรุณาระบุชื่อผู้ใช้",
    2034: "ไม่พบอีเมลนี้ในระบบ",
    2035: "รหัสผ่านใหม่และการยืนยันรหัสผ่านไม่ตรงกัน",
    2036: "รหัสผ่านใหม่ต้องไม่เหมือนกับรหัสผ่านปัจจุบัน",
    2037: "อีเมลนี้ถูกใช้งานแล้วโดยสมาชิกคนอื่น",
    2038: "เบอร์โทรศัพท์นี้ถูกใช้งานแล้วโดยสมาชิกคนอื่น",
    2039: "หมายเลขบัตรประชาชนต้องเป็นตัวเลข 13 หลักเท่านั้น",
    2040: "หมายเลขบัตรประชาชนนี้ถูกใช้งานแล้วโดยสมาชิกคนอื่น",
    2041: "ไม่พบประเภทสมาชิกที่ระบุ",
    2042: "ไม่พบหน่วยงานภาครัฐที่ระบุ",
    2043: "ไม่พบกระทรวงที่ระบุ",
    2044: "ไม่พบกรมที่ระบุ",
    2045: "ค่าการแจ้งเตือนต้องเป็น 'Y' หรือ 'N' เท่านั้น",
    2046: "ภาษาต้องเป็น 'th' หรือ 'en' เท่านั้น",

    # Database errors (3000-3099)
    3000: "เกิดข้อผิดพลาดในฐานข้อมูล",
    3001: "ไม่สามารถเชื่อมต่อฐานข้อมูลได้",
    3002: "ไม่พบข้อมูล",
    3005: "ไม่สามารถบันทึกข้อมูลได้",

    # API errors (4000-4099)
    4000: "ไม่สามารถเชื่อมต่อกับ ThaID ได้",
    4001: "API key ไม่ถูกต้อง",
    4003: "เกินขีดจำกัดการเรียก API",
    4004: "ไม่พบข้อมูลการตอบกลับ",
    4005: "ไม่พบข้อมูลบุคคลจากการตอบกลับ",
    4006: "ไม่พบข้อมูลสมาชิกบนระบบ กรุณาสมัครสมาชิก",

    # System errors (5000-5099)
    5000: "เกิดข้อผิดพลาดในระบบ",

}

# Payment status mapping
PAYMENT_STATUS = {
    '0': 'รอชำระ',
    '1': 'รอตรวจสอบ',
    '2': 'ยืนยันการชำระ',
    '3': 'ไม่อนุมัติ',
}

PAYMENT_DESCRIPTION = {
    1: 'ค่าธรรมเนียมการขึ้นทะเบียนที่ปรึกษา',
    2: 'ค่าธรรมเนียมการต่อทะเบียนที่ปรึกษา',
}

# Status work mapping
STATUS_WORK_MAPPING = {
    "0": 'ยื่นเรื่อง',
    "1": 'เจ้าหน้าที่กำลังตรวจสอบ',
    "2": 'ตรวจสอบสำเร็จ',
    "3": 'เปิดการแก้ไขข้อมูล',
    "4": 'รอชำระเงิน',
    "5": 'รออนุมัติ',
    "6": 'รออนุมัติ',
    "7": 'ยื่นเรื่องสำเร็จ',
    "8": 'คืนเรื่อง',
}

STATUS_WORK_LOG_MAPPING = {
    "0": 'ยื่นเรื่อง',
    "1": 'เจ้าหน้าที่กำลังตรวจสอบ',
    "2": 'ตรวจสอบสำเร็จ',
    "3": 'เปิดการแก้ไขข้อมูล',
    "4": 'รอชำระเงิน',
    "5": 'รออนุมัติ',
    "6": 'อนุมัติ',
    "7": 'ยื่นเรื่องสำเร็จ',
    "8": 'คืนเรื่อง',
    "9": 'แก้ไขข้อมูลสำเร็จ',
}
