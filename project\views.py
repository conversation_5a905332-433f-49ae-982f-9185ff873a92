from django.shortcuts import render
from rest_framework import status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from django.utils import timezone
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
import logging
from MCDC.models import TcdCorporateType
from MCDC.serializers import TcdCorporateTypeSerializer
from utils.response import APIResponse, get_language_from_request
from .serializers import (
    ProjectCountRequestSerializer,
    ProjectCountResponseSerializer,
    ProjectSearchRequestSerializer,
    ProjectDetailRequestSerializer,
    ProjectDetailResponseSerializer,
    ProjectViewCountResponseSerializer,
    ProjectViewIncrementResponseSerializer,
    MemberProjectSearchRequestSerializer,
    MemberProjectSearchResponseSerializer,
    SuitableConsultantRequestSerializer,
    SuitableConsultantResponseSerializer,
    ProjectInterestedConsultantsRequestSerializer,
    ProjectInterestedConsultantsResponseSerializer,
    ProjectFavoriteConsultantsRequestSerializer,
    ProjectFavoriteConsultantsResponseSerializer,
    ConsultantExperienceRequestSerializer,
    ConsultantExperienceResponseSerializer,
    ConsultantMemberViewResponseSerializer,
    ConsultantMemberViewRequestSerializer,
    ProjectMemberFavoriteRequestSerializer,
    ProjectMemberFavoriteStatusResponseSerializer,
    ProjectMemberFavoriteUpdateResponseSerializer,
    ConsultantSuitableProjectsRequestSerializer,
    ConsultantSuitableProjectsResponseSerializer,
    ConsultantFavoriteProjectsRequestSerializer,
    ConsultantFavoriteProjectsResponseSerializer,
    ConsultantFavoriteStatusResponseSerializer,
    ConsultantFavoriteUpdateResponseSerializer,
    ProjectDetailExtendedRequestSerializer,
    ProjectDetailExtendedResponseSerializer,
    ProjectInterestStatusRequestSerializer,
    ProjectInterestStatusResponseSerializer,
    ProjectInterestUpdateRequestSerializer,
    ProjectInterestUpdateResponseSerializer,
)
from .services import (
    ProjectViewService, 
    ProjectMatchingViewService, 
    ProjectInterestService,
    ProjectSearchService,
    ProjectCountService,
    ProjectDetailService,
    ProjectDetailExtendedService,
    MemberProjectSearchService,
    SuitableConsultantService,
    ProjectInterestedConsultantsService,
    ProjectFavoriteConsultantsService,
    ConsultantExperienceService,
    ConsultantMemberViewService,
    ProjectMemberFavoriteService,
    ConsultantSuitableProjectsService,
    ConsultantFavoriteProjectsService,
    ConsultantFavoriteService,
)

logger = logging.getLogger(__name__)


@extend_schema(
    tags=["Project"],
    summary="Get project count with advanced filtering",
    description="Returns the total count of active projects with comprehensive filtering capabilities",
    request=ProjectCountRequestSerializer,
    responses={
        200: ProjectCountResponseSerializer,
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ข้อมูลที่ส่งมาไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 2000}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    examples=[
        OpenApiExample(
            'Basic Count',
            summary='Get total project count without filters',
            description='Returns total count of all active projects',
            value={}
        ),
        OpenApiExample(
            'Filtered Count',
            summary='Get project count with filters',
            description='Returns count of projects matching the specified criteria',
            value={
                "project_name": "development",
                "organization_name": "ministry",
                "sector_ids": "1,2,3",
                "project_period_start": "2024-01-01",
                "project_period_end": "2024-12-31"
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([])
def project_count(request):
    """
    Get project count with filtering capabilities
    """
    language = get_language_from_request(request)
    
    try:
        # Validate request data
        serializer = ProjectCountRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Project count validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Use ProjectCountService to count projects
        result = ProjectCountService.count_projects(serializer.validated_data)
        
        if result['success']:
            return APIResponse.success(
                data=result['data'],
                language=language,
                status_code=status.HTTP_200_OK
            )
        else:
            return APIResponse.error(
                error_code=result.get('error_code', 5000),
                language=language,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    except Exception as e:
        logger.error(f"Error in project count: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    tags=["Project"],
    summary="Advanced project search with comprehensive filtering and sorting",
    description="Advanced project search endpoint with comprehensive filtering, sorting, and detailed response data",
    request=ProjectSearchRequestSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "example": True},
                "data": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "project_id": {"type": "integer"},
                            "project_name": {"type": "string"},
                            "organization_name": {"type": "string"},
                            "announcement_period": {"type": "string"},
                            "sectors": {"type": "string"},
                            "view_count": {"type": "integer"},
                            "matching_result": {"type": "number", "nullable": True}
                        }
                    }
                }
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ข้อมูลที่ส่งมาไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 2000}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    parameters=[
        OpenApiParameter(
            name='page',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Page number for pagination (default: 1)'
        ),
        OpenApiParameter(
            name='page_size',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Number of items per page (default: 10, max: 100)'
        )
    ],
    examples=[
        OpenApiExample(
            'Basic Search',
            summary='Basic project name search',
            description='Search projects by name with default sorting',
            value={
                "project_name": "development"
            }
        ),
        OpenApiExample(
            'Advanced Search',
            summary='Advanced search with multiple filters',
            description='Search with multiple criteria and custom sorting',
            value={
                "project_name": "infrastructure",
                "organization_name": "ministry",
                "sector_ids": "1,2,3",
                "project_period_start": "2024-01-01",
                "project_period_end": "2024-12-31",
                "min_view_count": 10,
                "sort_by": "-view_count"
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([])
def project_search(request):
    """
    Advanced project search with filtering and sorting
    """
    language = get_language_from_request(request)
    
    try:
        # Validate request data
        serializer = ProjectSearchRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Project search validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Get pagination parameters from query string with validation
        try:
            page = int(request.GET.get('page', 1))
            if page <= 0:
                page = 1
        except (ValueError, TypeError):
            page = 1
            
        try:
            page_size = int(request.GET.get('page_size', 10))
            if page_size <= 0:
                page_size = 10
            elif page_size > 100:
                page_size = 100
        except (ValueError, TypeError):
            page_size = 10
        
        # Use ProjectSearchService to search projects
        result = ProjectSearchService.search_projects(
            filters=serializer.validated_data,
            page=page,
            page_size=page_size,
            language=language
        )
        
        if result['success']:
            # Format response similar to search views pattern
            results = result['data']['results']
            pagination = result['data']['pagination']
            
            # Return response with pagination fields at root level
            from rest_framework.response import Response
            response_data = {
                'success': True,
                'error_code': None,
                'error_message': None,
                'data': results,
                'page': pagination['page'],
                'per_page': pagination['page_size'],
                'total': pagination['total_count'],
                'has_next': pagination['has_next'],
                'api_version': 'v.0.0.1'
            }
            return Response(response_data, status=status.HTTP_200_OK)
        else:
            return APIResponse.error(
                error_code=result.get('error_code', 5000),
                language=language,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    except Exception as e:
        logger.error(f"Error in project search: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Get Project Detail",
    description="ดึงข้อมูลรายละเอียดโครงการ พร้อมข้อมูลหน่วยงาน สาขา ความเชี่ยวชาญ การบริการ และข้อมูลการ matching สำหรับที่ปรึกษา",
    parameters=[
        OpenApiParameter(
            name="project_id",
            type=int,
            location=OpenApiParameter.PATH,
            description="ID ของโครงการที่ต้องการดูรายละเอียด",
            required=True
        ),
    ],
    responses={
        200: ProjectDetailResponseSerializer,
        400: "Bad Request - Invalid project ID",
        404: "Not Found - Project not found",
        500: "Internal Server Error"
    },
    tags=["Project Detail"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def project_detail(request, project_id):
    """
    Get detailed project information
    """
    language = get_language_from_request(request)
    
    try:
        # Get user_consult_id if user is authenticated
        user_consult_id = None
        if request.user.is_authenticated:
            if hasattr(request.user, 'user_type') and request.user.user_type in ['consultant', 'consult']:
                user_consult_id = request.user.id
            elif hasattr(request.user, 'user_consult_id'):
                user_consult_id = request.user.user_consult_id
            else:
                # For JWT tokens, the user ID might directly be the consultant ID
                user_consult_id = request.user.id
        # Use ProjectDetailService to get project details
        result = ProjectDetailService.get_project_detail(project_id, user_consult_id, language)
        
        if result['success']:
            return APIResponse.success(
                data=result['data'],
                language=language,
                status_code=status.HTTP_200_OK
            )
        else:
            error_code = result.get('error_code', 5000)
            status_code = status.HTTP_404_NOT_FOUND if error_code == 3002 else status.HTTP_500_INTERNAL_SERVER_ERROR
            
            return APIResponse.error(
                error_code=error_code,
                language=language,
                status_code=status_code
            )
        
    except Exception as e:
        logger.error(f"Error in project detail: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Get Extended Project Detail",
    description="ดึงข้อมูลรายละเอียดโครงการแบบละเอียดพร้อมข้อมูล master data ทั้งหมด รวมถึงประสบการณ์ที่ปรึกษา ผลลัพธ์ ค่าใช้จ่าย ประเภทโครงการ และอื่นๆ",
    request=ProjectDetailExtendedRequestSerializer,
    responses={
        200: ProjectDetailExtendedResponseSerializer,
        400: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ข้อมูลที่ส่งมาไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 2000}
            }
        },
        404: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่พบโครงการ"},
                "error_code": {"type": "integer", "example": 3002}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    examples=[
        OpenApiExample(
            'Basic Request',
            summary='ดึงข้อมูลโครงการแบบละเอียด',
            description='ดึงข้อมูลโครงการพร้อม master data ทั้งหมด',
            value={
                "app_project_id": 123
            }
        )
    ],
    tags=["Project Detail Extended"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def get_project_detail_extended(request):
    """
    Get extended project detail information with all master data relationships
    """
    language = get_language_from_request(request)
    
    try:
        # Validate request data
        serializer = ProjectDetailExtendedRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Project detail extended validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Get project ID from validated data
        app_project_id = serializer.validated_data['app_project_id']
        
        # Get user_consult_id from JWT token if authenticated
        user_consult_id = None
        if request.user.is_authenticated:
            if hasattr(request.user, 'user_type') and request.user.user_type in ['consultant', 'consult']:
                user_consult_id = request.user.id
            elif hasattr(request.user, 'user_consult_id'):
                user_consult_id = request.user.user_consult_id
            else:
                # For JWT tokens, the user ID might directly be the consultant ID
                user_consult_id = request.user.id
        
        # Use ProjectDetailExtendedService to get project details
        result = ProjectDetailExtendedService.get_project_detail_extended(
            app_project_id=app_project_id,
            user_consult_id=user_consult_id,
            language=language
        )
        
        if result['success']:
            return APIResponse.success(
                data=result['data'],
                language=language,
                status_code=status.HTTP_200_OK
            )
        else:
            error_code = result.get('error_code', 5000)
            status_code = status.HTTP_404_NOT_FOUND if error_code == 3002 else status.HTTP_500_INTERNAL_SERVER_ERROR
            
            return APIResponse.error(
                error_code=error_code,
                language=language,
                status_code=status_code
            )
        
    except Exception as e:
        logger.error(f"Error in get project detail extended: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="เพิ่มจำนวนการเข้าชมโครงการ",
    description="""
    API สำหรับเพิ่มจำนวนการเข้าชมโครงการ (เพิ่ม 1)

    **Features:**
    - เพิ่มจำนวนการเข้าชมโครงการ 1 ครั้ง
    - ตรวจสอบการมีอยู่ของโครงการ
    - ส่งคืนจำนวนการเข้าชมเดิมและใหม่
    - Error handling ที่ครอบคลุม
    """,
    parameters=[
        OpenApiParameter(
            name='project_id',
            description='ID ของโครงการ',
            required=True,
            type=int,
            location=OpenApiParameter.PATH
        )
    ],
    responses={
        200: ProjectViewIncrementResponseSerializer,
        404: {
            "description": "ไม่พบโครงการ",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_code": 3002,
                        "message": "Data not found"
                    }
                }
            }
        },
        500: {
            "description": "ข้อผิดพลาดของระบบ",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_code": 5000,
                        "message": "System error"
                    }
                }
            }
        }
    },
    tags=["Project"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([])
def increment_project_view(request, project_id):
    """
    Increment project view count by 1
    """
    try:
        result = ProjectViewService.increment_project_view(project_id)
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_404_NOT_FOUND)
            
    except Exception as e:
        logger.error(f"Error incrementing project view: {str(e)}")
        return Response({
            'success': False,
            'error_code': 5000,
            'message': 'System error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=["Project Matching"],
    summary="Increment project matching view count",
    description="อัปเดตจำนวนการเข้าชมโครงการที่ matching (เพิ่ม 1)",
    parameters=[
        OpenApiParameter(
            name="app_project_id",
            type=int,
            location=OpenApiParameter.PATH,
            description="ID ของโครงการ",
            required=True
        ),
        OpenApiParameter(
            name="user_consult_id",
            type=int,
            location=OpenApiParameter.PATH,
            description="ID ของที่ปรึกษาที่ log in",
            required=True
        )
    ],
    responses={
        200: {
            "description": "Success",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "data": {
                            "id": 1,
                            "app_project_id": 123,
                            "user_consult_id": 456,
                            "old_consult_view": 5,
                            "new_consult_view": 6,
                            "matching": 85.5,
                            "updated": True
                        },
                        "message": "Success"
                    }
                }
            }
        },
        404: {
            "description": "Project matching not found",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_code": 3002,
                        "error_message": "Data not found"
                    }
                }
            }
        }
    }
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def increment_project_matching_view(request, app_project_id, user_consult_id):
    """
    Increment project matching view count by 1
    """
    try:
        result = ProjectMatchingViewService.increment_project_matching_view(app_project_id, user_consult_id)
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_404_NOT_FOUND)
            
    except Exception as e:
        logger.error(f"Error incrementing project matching view: {str(e)}")
        return Response({
            'success': False,
            'error_code': 5000,
            'message': 'System error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Get Project Interest Status",
    description="หาสถานะการยื่นความสนใจเดิม (ดึง user_consult_id จาก JWT token)",
    request=ProjectInterestStatusRequestSerializer,
    responses={
        200: ProjectInterestStatusResponseSerializer,
        400: {
            "description": "ข้อมูลที่ส่งมาไม่ถูกต้อง",
            "content": {
                "application/json": {
                    "example": {
                        "status": False,
                        "error_message": "ข้อมูลที่ส่งมาไม่ถูกต้อง",
                        "error_code": 2000,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        },
        401: {
            "description": "ไม่ได้รับอนุญาต",
            "content": {
                "application/json": {
                    "example": {
                        "status": False,
                        "error_message": "Unauthorized",
                        "error_code": 4001,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        },
        404: {
            "description": "ไม่พบข้อมูล",
            "content": {
                "application/json": {
                    "example": {
                        "status": False,
                        "error_message": "ไม่พบข้อมูล",
                        "error_code": 3002,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        }
    },
    examples=[
        OpenApiExample(
            'Basic Request',
            summary='ตรวจสอบสถานะความสนใจ',
            description='ตรวจสอบสถานะการยื่นความสนใจของที่ปรึกษาต่อโครงการ (user_consult_id จะถูกดึงจาก JWT token)',
            value={
                "app_project_id": 456
            }
        )
    ],
    tags=["Project Interest"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_project_interest_status(request):
    """
    Get current interest status for a project-consultant pair
    """
    try:
        # Check if user is authenticated and is a consultant
        if not request.user.is_authenticated:
            return Response({
                'status': False,
                'error_message': 'Unauthorized',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Get user_consult_id from JWT token
        user_consult_id = None
        if hasattr(request.user, 'user_type') and request.user.user_type in ['consultant', 'consult']:
            user_consult_id = request.user.id
        elif hasattr(request.user, 'user_consult_id'):
            user_consult_id = request.user.user_consult_id
        else:
            # For JWT tokens, the user ID might directly be the consultant ID
            user_consult_id = request.user.id
        
        if not user_consult_id:
            return Response({
                'status': False,
                'error_message': 'Unauthorized - Consultant ID not found in token',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Validate request data
        serializer = ProjectInterestStatusRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Project interest status validation failed: {serializer.errors}")
            return Response({
                'status': False,
                'error_message': 'ข้อมูลที่ส่งมาไม่ถูกต้อง',
                'error_code': 2000,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        app_project_id = serializer.validated_data['app_project_id']
        
        result = ProjectInterestService.get_project_interest_status(app_project_id, user_consult_id)
        
        if result['success']:
            return Response({
                'status': True,
                'error_message': None,
                'error_code': None,
                'data': result['data'],
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'status': False,
                'error_message': result['message'],
                'error_code': result['error_code'],
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_404_NOT_FOUND)
            
    except Exception as e:
        logger.error(f"Error getting project interest status: {str(e)}")
        return Response({
            'status': False,
            'error_message': 'System error',
            'error_code': 5000,
            'data': {},
            'api_version': 'v.0.0.1'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Update Project Interest Status",
    description="อัปเดตสถานะการยื่นความสนใจโครงการ (Toggle: ถ้าเดิม = 0 เปลี่ยนเป็น 1, ถ้าไม่ใช่ เปลี่ยนเป็น 0) (ดึง user_consult_id จาก JWT token)",
    request=ProjectInterestUpdateRequestSerializer,
    responses={
        200: ProjectInterestUpdateResponseSerializer,
        400: {
            "description": "ข้อมูลที่ส่งมาไม่ถูกต้อง",
            "content": {
                "application/json": {
                    "example": {
                        "status": False,
                        "error_message": "ข้อมูลที่ส่งมาไม่ถูกต้อง",
                        "error_code": 2000,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        },
        401: {
            "description": "ไม่ได้รับอนุญาต",
            "content": {
                "application/json": {
                    "example": {
                        "status": False,
                        "error_message": "Unauthorized",
                        "error_code": 4001,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        },
        404: {
            "description": "ไม่พบข้อมูล",
            "content": {
                "application/json": {
                    "example": {
                        "status": False,
                        "error_message": "ไม่พบข้อมูล",
                        "error_code": 3002,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        }
    },
    examples=[
        OpenApiExample(
            'Toggle Interest Status',
            summary='อัปเดตสถานะความสนใจ',
            description='Toggle สถานะการยื่นความสนใจของที่ปรึกษาต่อโครงการ (user_consult_id จะถูกดึงจาก JWT token)',
            value={
                "app_project_id": 456
            }
        )
    ],
    tags=["Project Interest"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_project_interest_status(request):
    """
    Toggle project interest status (0 -> 1, other -> 0)
    """
    try:
        # Check if user is authenticated and is a consultant
        if not request.user.is_authenticated:
            return Response({
                'status': False,
                'error_message': 'Unauthorized',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Get user_consult_id from JWT token
        user_consult_id = None
        if hasattr(request.user, 'user_type') and request.user.user_type in ['consultant', 'consult']:
            user_consult_id = request.user.id
        elif hasattr(request.user, 'user_consult_id'):
            user_consult_id = request.user.user_consult_id
        else:
            # For JWT tokens, the user ID might directly be the consultant ID
            user_consult_id = request.user.id
        
        if not user_consult_id:
            return Response({
                'status': False,
                'error_message': 'Unauthorized - Consultant ID not found in token',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Validate request data
        serializer = ProjectInterestUpdateRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Project interest update validation failed: {serializer.errors}")
            return Response({
                'status': False,
                'error_message': 'ข้อมูลที่ส่งมาไม่ถูกต้อง',
                'error_code': 2000,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        app_project_id = serializer.validated_data['app_project_id']
        
        result = ProjectInterestService.update_project_interest_status(app_project_id, user_consult_id, request=request)
        
        if result['success']:
            return Response({
                'status': True,
                'error_message': None,
                'error_code': None,
                'data': result['data'],
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'status': False,
                'error_message': result['message'],
                'error_code': result['error_code'],
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_404_NOT_FOUND)
            
    except Exception as e:
        logger.error(f"Error updating project interest status: {str(e)}")
        return Response({
            'status': False,
            'error_message': 'System error',
            'error_code': 5000,
            'data': {},
            'api_version': 'v.0.0.1'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=["Member Project"],
    summary="Search member's projects",
    description="ค้นหาโครงการของสมาชิก พร้อมการกรองข้อมูลและการเรียงลำดับ",
    request=MemberProjectSearchRequestSerializer,
    responses={
        200: MemberProjectSearchResponseSerializer,
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ข้อมูลที่ส่งมาไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 2000}
            }
        },
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่ได้รับอนุญาต"},
                "error_code": {"type": "integer", "example": 4001}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    parameters=[
        OpenApiParameter(
            name='page',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Page number for pagination (default: 1)'
        ),
        OpenApiParameter(
            name='page_size',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Number of items per page (default: 10, max: 100)'
        )
    ],
    examples=[
        OpenApiExample(
            'Latest Projects',
            summary='ค้นหาโครงการล่าสุด',
            description='ค้นหาโครงการล่าสุดตามชื่อโครงการ',
            value={
                "project_name": "โครงการพัฒนา",
                "filter_type": "latest"
            }
        ),
        OpenApiExample(
            'Published Projects',
            summary='ค้นหาโครงการที่ประกาศ',
            description='ค้นหาโครงการที่มีสถานะเผยแพร่แล้ว',
            value={
                "project_name": "โครงการ",
                "announcement_start_date": "2024-01-01",
                "announcement_end_date": "2024-12-31",
                "filter_type": "published"
            }
        ),
        OpenApiExample(
            'Announcement Count Filter',
            summary='กรองตามจำนวนที่ประกาศ',
            description='เรียงโครงการตามจำนวนการเข้าชม (view count)',
            value={
                "filter_type": "announcement_count"
            }
        ),
        OpenApiExample(
            'Date Range Search',
            summary='ค้นหาตามช่วงวันที่ประกาศ',
            description='ค้นหาโครงการในช่วงวันที่ประกาศที่กำหนด',
            value={
                "announcement_start_date": "2024-01-01",
                "announcement_end_date": "2024-06-30",
                "filter_type": "latest"
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def member_project_search(request):
    """
    Search member's projects with filtering and sorting using custom pagination
    """
    from utils.pagination import CustomPagination
    from django.db.models import Count, Q
    from datetime import datetime
    
    language = get_language_from_request(request)
    
    try:
        # Check if user is authenticated and is a member
        if not request.user.is_authenticated:
            return APIResponse.error(
                error_code=4001,
                language=language,
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        
        # Get app_member_id from authenticated user
        app_member_id = None
        if hasattr(request.user, 'user_type') and request.user.user_type == 'member':
            app_member_id = request.user.id
        elif hasattr(request.user, 'app_member_id'):
            app_member_id = request.user.app_member_id
        else:
            # Try to get from user's related member record
            try:
                from authentication.models import TcdAppMember
                member = TcdAppMember.objects.filter(user_id=request.user.id).first()
                if member:
                    app_member_id = member.id
            except:
                pass
        
        if not app_member_id:
            return APIResponse.error(
                error_code=4001,
                language=language,
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        
        # Validate request data
        serializer = MemberProjectSearchRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Member project search validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Build base queryset - only completed projects for the member
        from .models import TcdAppProject, TcdAppProjectConsult
        queryset = TcdAppProject.objects.filter(
            app_member_id=app_member_id,
            is_complete=True
        ).select_related('app_member')
        
        # Apply search filters
        filters = serializer.validated_data
        if filters.get('project_name'):
            queryset = queryset.filter(name__icontains=filters['project_name'])
        
        # Date filters
        if filters.get('announcement_start_date'):
            queryset = queryset.filter(start_date__gte=filters['announcement_start_date'])
        
        if filters.get('announcement_end_date'):
            # Convert date to datetime with end of day for proper comparison
            from datetime import datetime, time
            end_date = filters['announcement_end_date']
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            end_datetime = datetime.combine(end_date, time(23, 59, 59))
            queryset = queryset.filter(end_date__lte=end_datetime)
        
        # Status filter
        if filters.get('status'):
            queryset = queryset.filter(status=filters['status'])
        
        # Apply filtering and sorting based on filter_type
        filter_type = filters.get('filter_type', 'latest')
        
        if filter_type == 'latest':
            # โครงการล่าสุด - เรียงตาม create_date ล่าสุด
            queryset = queryset.order_by('-create_date')
        elif filter_type == 'published':
            # โครงการที่ประกาศ - เรียงตามสถานะที่เผยแพร่แล้ว
            queryset = queryset.filter(status='1').order_by('-status')
        elif filter_type == 'announcement_count':
            # จำนวนที่ประกาศ - เรียงตามจำนวนการเข้าชม (view count)
            queryset = queryset.order_by('-view', '-id')
        else:
            # Default: โครงการล่าสุด
            queryset = queryset.order_by('-id')
        
        # Setup custom pagination
        paginator = CustomPagination()
        
        # Paginate the queryset
        page = paginator.paginate_queryset(queryset, request)
        
        if page is not None:
            # Build response data for each project
            results = []
            for project in page:
                
                # Get status name
                status_name = MemberProjectSearchService._get_status_name(project.status, language)
                
                # Get matching count
                matching_count = MemberProjectSearchService._get_matching_count(project.id, project.result)
                
                # Get interested count
                interested_count = MemberProjectSearchService._get_interested_count(project.id)
                
                project_data = {
                    'project_id': int(project.id),  # Force convert to int
                    'project_name': project.name or '',
                    'announcement_start_date': project.start_date,
                    'announcement_end_date': project.end_date,
                    'status': int(project.status) if project.status.isdigit() else 0,
                    'status_name': status_name,
                    'matching_count': matching_count,
                    'interested_count': interested_count,
                    'view_count': project.view or 0
                }
                
                results.append(project_data)
            
            # Return paginated response using custom pagination
            return paginator.get_paginated_response(results)
        
        # Fallback if pagination fails
        return APIResponse.success(
            data={
                'results': [],
                'page': 1,
                'per_page': 10,
                'total': 0,
                'has_next': False
            },
            language=language,
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        logger.error(f"Error in member project search: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    tags=["Project Consultant"],
    summary="Get suitable consultants for a project",
    description="ดึงรายการที่ปรึกษาที่เหมาะสมสำหรับโครงการ พร้อมการกรองและเรียงลำดับ",
    request=SuitableConsultantRequestSerializer,
    responses={
        200: SuitableConsultantResponseSerializer,
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ข้อมูลที่ส่งมาไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 2000}
            }
        },
        404: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่พบโครงการ"},
                "error_code": {"type": "integer", "example": 3002}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    parameters=[
        OpenApiParameter(
            name='page',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Page number for pagination (default: 1)'
        ),
        OpenApiParameter(
            name='page_size',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Number of items per page (default: 10, max: 100)'
        )
    ],
    examples=[
        OpenApiExample(
            'Basic Consultant List',
            summary='ดึงรายการที่ปรึกษาพื้นฐาน',
            description='ดึงรายการที่ปรึกษาที่เหมาะสมโดยไม่มีการกรอง',
            value={}
        ),
        OpenApiExample(
            'Independent Consultants Only',
            summary='กรองเฉพาะที่ปรึกษาอิสระ',
            description='กรองที่ปรึกษาประเภทอิสระ (Type 1)',
            value={
                "consultant_type_filter_1": 1
            }
        ),
        OpenApiExample(
            'Corporate Consultants Only',
            summary='กรองเฉพาะที่ปรึกษานิติบุคคล',
            description='กรองที่ปรึกษาประเภทนิติบุคคล (Type 2)',
            value={
                "consultant_type_filter_1": 2
            }
        ),
        OpenApiExample(
            'Specific Corporate Type',
            summary='กรองตามประเภทกิจการเฉพาะ',
            description='กรองที่ปรึกษานิติบุคคลตามประเภทกิจการ (เช่น ห้างหุ่นส่วน, สถาบันการศึกษา)',
            value={
                "consultant_type_filter_1": 2,
                "consultant_type_filter_2": "1"
            }
        ),
        OpenApiExample(
            'Sort by Matching Score',
            summary='เรียงตามผลการจับคู่ (มาก-น้อย)',
            description='เรียงรายการที่ปรึกษาตามผลการจับคู่จากมากไปน้อย',
            value={
                "sort_by_matching": True
            }
        ),
        OpenApiExample(
            'Sort by Latest Consultants',
            summary='เรียงตามที่ปรึกษาล่าสุด',
            description='เรียงรายการที่ปรึกษาตามความใหม่ของการลงทะเบียน',
            value={
                "sort_by_latest": True
            }
        ),
        OpenApiExample(
            'Combined Filters',
            summary='การกรองแบบรวม',
            description='กรองที่ปรึกษานิติบุคคลประเภทเฉพาะและเรียงตามผลการจับคู่',
            value={
                "consultant_type_filter_1": 2,
                "consultant_type_filter_2": "3",
                "sort_by_matching": True
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_suitable_consultants(request, project_id):
    """
    Get suitable consultants for a project with filtering and sorting
    """
    language = get_language_from_request(request)
    
    try:
        # Validate request data
        serializer = SuitableConsultantRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Suitable consultant validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Get pagination parameters from query string with validation
        try:
            page = int(request.GET.get('page', 1))
            if page <= 0:
                page = 1
        except (ValueError, TypeError):
            page = 1
            
        try:
            page_size = int(request.GET.get('page_size', 10))
            if page_size <= 0:
                page_size = 10
            elif page_size > 100:
                page_size = 100
        except (ValueError, TypeError):
            page_size = 10
        
        # Extract filters from validated data
        validated_data = serializer.validated_data
        consultant_type_filter_1 = validated_data.get('consultant_type_filter_1')
        consultant_type_filter_2 = validated_data.get('consultant_type_filter_2')
        sort_by_matching = validated_data.get('sort_by_matching', False)
        sort_by_latest = validated_data.get('sort_by_latest', False)
        
        # Use SuitableConsultantService to get consultants
        result = SuitableConsultantService.get_suitable_consultants(
            project_id=project_id,
            consultant_type_filter_1=consultant_type_filter_1,
            consultant_type_filter_2=consultant_type_filter_2,
            sort_by_matching=sort_by_matching,
            sort_by_latest=sort_by_latest,
            page=page,
            page_size=page_size
        )
        
        if result['success']:
            # Return response with standard API format but pagination fields at root level
            from rest_framework.response import Response
            response_data = {
                'status': True,
                'error_message': None,
                'error_code': None,
                'data': result['data'],
                'page': result['page'],
                'per_page': result['per_page'],
                'total': result['total'],
                'has_next': result['has_next'],
                'api_version': 'v.0.0.1'
            }
            return Response(response_data, status=status.HTTP_200_OK)
        else:
            error_code = result.get('error_code', 5000)
            status_code = status.HTTP_404_NOT_FOUND if error_code == 3002 else status.HTTP_500_INTERNAL_SERVER_ERROR
            
            return APIResponse.error(
                error_code=error_code,
                language=language,
                status_code=status_code
            )
        
    except Exception as e:
        logger.error(f"Error in get suitable consultants: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Get corporate type",
    description="ดึงข้อมูลประเภทกิจการ",
    responses={
        200: TcdCorporateTypeSerializer,
    },
    tags=["Corporate Type"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_corporate_type(request):
    """
    Get corporate type
    """
    try:
        language = get_language_from_request(request)
        corporate_type = TcdCorporateType.objects.all()
        serializer = TcdCorporateTypeSerializer(corporate_type, many=True)
        return APIResponse.success(data=serializer.data, language=language, status_code=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error in get_corporate_type: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language='th',
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    tags=["Project Interested Consultants"],
    summary="Get interested consultants for a project",
    description="ดึงรายการที่ปรึกษาที่สนใจโครงการ (consult_send = 1) พร้อม pagination และตัวเลือกการเรียงลำดับ",
    request=ProjectInterestedConsultantsRequestSerializer,
    responses={
        200: ProjectInterestedConsultantsResponseSerializer,
        404: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่พบโครงการ"},
                "error_code": {"type": "integer", "example": 3002}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    parameters=[
        OpenApiParameter(
            name='page',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Page number for pagination (default: 1)'
        ),
        OpenApiParameter(
            name='page_size',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Number of items per page (default: 10, max: 100)'
        )
    ],
    examples=[
        OpenApiExample(
            'Basic Request',
            summary='ดึงรายการผู้สนใจโครงการพื้นฐาน',
            description='ดึงรายการที่ปรึกษาที่สนใจโครงการโดยไม่มีการกรอง',
            value={}
        ),
        OpenApiExample(
            'Sort by Matching Request',
            summary='ดึงรายการผู้สนใจโครงการเรียงตามผลการจับคู่',
            description='ดึงรายการที่ปรึกษาที่สนใจโครงการเรียงลำดับตามผลการจับคู่จากมากไปน้อย',
            value={
                "sort_by_matching": True
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_project_interested_consultants(request, project_id):
    """
    Get list of consultants interested in a project with pagination
    """
    language = get_language_from_request(request)
    
    try:
        # Validate request data
        serializer = ProjectInterestedConsultantsRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Project interested consultants validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Get sorting parameter from validated data
        sort_by_matching = serializer.validated_data.get('sort_by_matching', False)
        
        # Get pagination parameters from query string with validation
        try:
            page = int(request.GET.get('page', 1))
            if page <= 0:
                page = 1
        except (ValueError, TypeError):
            page = 1
            
        try:
            page_size = int(request.GET.get('page_size', 10))
            if page_size <= 0:
                page_size = 10
            elif page_size > 100:
                page_size = 100
        except (ValueError, TypeError):
            page_size = 10
        
        # Use ProjectInterestedConsultantsService to get interested consultants
        result = ProjectInterestedConsultantsService.get_interested_consultants(
            project_id=project_id,
            page=page,
            page_size=page_size,
            sort_by_matching=sort_by_matching
        )
        
        if result['success']:
            # Return response with pagination fields at root level
            from rest_framework.response import Response
            response_data = {
                'success': True,
                'error_code': None,
                'error_message': None,
                'data': {
                    'results': result['data']['results'],
                    'project_info': result['data']['project_info']
                },
                'page': result['data']['pagination']['page'],
                'per_page': result['data']['pagination']['page_size'],
                'total': result['data']['pagination']['total_count'],
                'has_next': result['data']['pagination']['has_next'],
                'api_version': 'v.0.0.1'
            }
            return Response(response_data, status=status.HTTP_200_OK)
        else:
            error_code = result.get('error_code', 5000)
            status_code = status.HTTP_404_NOT_FOUND if error_code == 3002 else status.HTTP_500_INTERNAL_SERVER_ERROR
            
            return APIResponse.error(
                error_code=error_code,
                language=language,
                status_code=status_code
            )
        
    except Exception as e:
        logger.error(f"Error in get project interested consultants: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    tags=["Project Favorite Consultants"],
    summary="Get favorite consultants for a project",
    description="ดึงรายการที่ปรึกษาที่ชอบในโครงการ (member_favorite = '1') พร้อม pagination และตัวเลือกการเรียงลำดับ",
    request=ProjectFavoriteConsultantsRequestSerializer,
    responses={
        200: ProjectFavoriteConsultantsResponseSerializer,
        404: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่พบโครงการ"},
                "error_code": {"type": "integer", "example": 3002}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    parameters=[
        OpenApiParameter(
            name='page',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Page number for pagination (default: 1)'
        ),
        OpenApiParameter(
            name='page_size',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Number of items per page (default: 10, max: 100)'
        )
    ],
    examples=[
        OpenApiExample(
            'Basic Request',
            summary='ดึงรายการที่ปรึกษาที่ชอบพื้นฐาน',
            description='ดึงรายการที่ปรึกษาที่ชอบในโครงการโดยไม่มีการกรอง',
            value={}
        ),
        OpenApiExample(
            'Sort by Matching Request',
            summary='ดึงรายการที่ปรึกษาที่ชอบเรียงตามผลการจับคู่',
            description='ดึงรายการที่ปรึกษาที่ชอบในโครงการเรียงลำดับตามผลการจับคู่จากมากไปน้อย',
            value={
                "sort_by_matching": True
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_project_favorite_consultants(request, project_id):
    """
    Get list of favorite consultants for a project with pagination
    """
    language = get_language_from_request(request)
    
    try:
        # Validate request data
        serializer = ProjectFavoriteConsultantsRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Project favorite consultants validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Get sorting parameter from validated data
        sort_by_matching = serializer.validated_data.get('sort_by_matching', False)
        
        # Get pagination parameters from query string with validation
        try:
            page = int(request.GET.get('page', 1))
            if page <= 0:
                page = 1
        except (ValueError, TypeError):
            page = 1
            
        try:
            page_size = int(request.GET.get('page_size', 10))
            if page_size <= 0:
                page_size = 10
            elif page_size > 100:
                page_size = 100
        except (ValueError, TypeError):
            page_size = 10
        
        # Use ProjectFavoriteConsultantsService to get favorite consultants
        result = ProjectFavoriteConsultantsService.get_favorite_consultants(
            project_id=project_id,
            page=page,
            page_size=page_size,
            sort_by_matching=sort_by_matching
        )
        
        if result['success']:
            # Return response with pagination fields at root level
            from rest_framework.response import Response
            response_data = {
                'success': True,
                'error_code': None,
                'error_message': None,
                'data': {
                    'results': result['data']['results'],
                    'project_info': result['data']['project_info']
                },
                'page': result['data']['pagination']['page'],
                'per_page': result['data']['pagination']['page_size'],
                'total': result['data']['pagination']['total_count'],
                'has_next': result['data']['pagination']['has_next'],
                'api_version': 'v.0.0.1'
            }
            return Response(response_data, status=status.HTTP_200_OK)
        else:
            error_code = result.get('error_code', 5000)
            status_code = status.HTTP_404_NOT_FOUND if error_code == 3002 else status.HTTP_500_INTERNAL_SERVER_ERROR
            
            return APIResponse.error(
                error_code=error_code,
                language=language,
                status_code=status_code
            )
        
    except Exception as e:
        logger.error(f"Error in get project favorite consultants: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    tags=["Consultant Experience"],
    summary="Get consultant experience projects",
    description="ดึงรายการประสบการณ์ทั้งหมดของที่ปรึกษา พร้อมข้อมูลสาขา ความเชี่ยวชาญ และบริการของแต่ละโครงการ",
    request=ConsultantExperienceRequestSerializer,
    responses={
        200: ConsultantExperienceResponseSerializer,
        404: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่พบที่ปรึกษา"},
                "error_code": {"type": "integer", "example": 3002}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    parameters=[
        OpenApiParameter(
            name='page',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Page number for pagination (default: 1)'
        ),
        OpenApiParameter(
            name='page_size',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Number of items per page (default: 10, max: 100)'
        )
    ],
    examples=[
        OpenApiExample(
            'Independent Consultant Request',
            summary='ดึงรายการประสบการณ์ที่ปรึกษาอิสระ',
            description='ดึงรายการประสบการณ์ทั้งหมดของที่ปรึกษาอิสระ',
            value={
                "user_consult_id": 123,
                "consult_type": 1,
                "corporate_type_id": 456
            }
        ),
        OpenApiExample(
            'Corporate Consultant Request',
            summary='ดึงรายการประสบการณ์ที่ปรึกษานิติบุคคล',
            description='ดึงรายการประสบการณ์ทั้งหมดของที่ปรึกษานิติบุคคล',
            value={
                "user_consult_id": 789,
                "consult_type": 2,
                "corporate_type_id": 101
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def get_consultant_experience(request):
    """
    Get all consultant experience projects for the specified consultant
    """
    language = get_language_from_request(request)
    
    try:
        # Validate request data
        serializer = ConsultantExperienceRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Consultant experience validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Get parameters from validated data
        validated_data = serializer.validated_data
        project_id = validated_data['project_id']
        general_data_id = validated_data['general_data_id']
        user_consult_id = validated_data['user_consult_id']
        consult_type = validated_data['consult_type']
        corporate_type_id = validated_data['corporate_type_id']
        
        # Get pagination parameters from query string with validation
        try:
            page = int(request.GET.get('page', 1))
            if page <= 0:
                page = 1
        except (ValueError, TypeError):
            page = 1
            
        try:
            page_size = int(request.GET.get('page_size', 10))
            if page_size <= 0:
                page_size = 10
            elif page_size > 100:
                page_size = 100
        except (ValueError, TypeError):
            page_size = 10
        
        # Use ConsultantExperienceService to get consultant experience
        result = ConsultantExperienceService.get_consultant_experience(
            project_id=project_id,
            general_data_id=general_data_id,
            user_consult_id=user_consult_id,
            consult_type=consult_type,
            corporate_type_id=corporate_type_id,
            page=page,
            page_size=page_size,
            language=language
        )
        
        if result['success']:
            # Return response with pagination fields at root level
            from rest_framework.response import Response
            response_data = {
                'success': True,
                'error_code': None,
                'error_message': None,
                'data': result['data']['results'],
                'page': result['data']['pagination']['page'],
                'per_page': result['data']['pagination']['page_size'],
                'total': result['data']['pagination']['total_count'],
                'has_next': result['data']['pagination']['has_next'],
                'api_version': 'v.0.0.1'
            }
            return Response(response_data, status=status.HTTP_200_OK)
        else:
            error_code = result.get('error_code', 5000)
            status_code = status.HTTP_404_NOT_FOUND if error_code == 3002 else status.HTTP_500_INTERNAL_SERVER_ERROR
            
            return APIResponse.error(
                error_code=error_code,
                language=language,
                status_code=status_code
            )
        
    except Exception as e:
        logger.error(f"Error in get consultant experience: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    tags=["Consultant Member View"],
    summary="Increment consultant member view count",
    description="อัปเดตจำนวนการเข้าชมที่ปรึกษาโดยสมาชิก (เพิ่ม 1)",
    request=ConsultantMemberViewRequestSerializer,
    responses={
        200: ConsultantMemberViewResponseSerializer,
        400: {
            "description": "ข้อมูลที่ส่งมาไม่ถูกต้อง",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_code": 2000,
                        "error_message": "ข้อมูลที่ส่งมาไม่ถูกต้อง"
                    }
                }
            }
        },
        404: {
            "description": "ไม่พบข้อมูล",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_code": 3002,
                        "error_message": "Data not found"
                    }
                }
            }
        },
        500: {
            "description": "ข้อผิดพลาดของระบบ",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_code": 5000,
                        "error_message": "System error"
                    }
                }
            }
        }
    },
    examples=[
        OpenApiExample(
            'Basic Request',
            summary='อัปเดตจำนวนการเข้าชมที่ปรึกษา',
            description='เพิ่มจำนวนการเข้าชมที่ปรึกษาโดยสมาชิก 1 ครั้ง',
            value={
                "app_project_id": 123,
                "user_consult_id": 456
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def increment_consultant_member_view(request):
    """
    Increment consultant member view count by 1
    """
    language = get_language_from_request(request)
    
    try:
        # Validate request data
        serializer = ConsultantMemberViewRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Consultant member view validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Get parameters from validated data
        validated_data = serializer.validated_data
        app_project_id = validated_data['app_project_id']
        user_consult_id = validated_data['user_consult_id']
        
        # Check if user is authenticated and is a member
        if not request.user.is_authenticated:
            return APIResponse.error(
                error_code=4001,
                language=language,
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        
        # Get app_member_id from authenticated user
        app_member_id = None
        if hasattr(request.user, 'user_type') and request.user.user_type == 'member':
            app_member_id = request.user.id
        elif hasattr(request.user, 'app_member_id'):
            app_member_id = request.user.app_member_id
        else:
            # Try to get from user's related member record
            try:
                from authentication.models import TcdAppMember
                member = TcdAppMember.objects.filter(user_id=request.user.id).first()
                if member:
                    app_member_id = member.id
            except:
                pass
        
        if not app_member_id:
            return APIResponse.error(
                error_code=4001,
                language=language,
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        
        # Use ConsultantMemberViewService to increment view count
        result = ConsultantMemberViewService.increment_consultant_member_view(
            app_project_id=app_project_id,
            user_consult_id=user_consult_id,
            app_member_id=app_member_id
        )
        
        if result['success']:
            return APIResponse.success(
                data=result['data'],
                language=language,
                status_code=status.HTTP_200_OK
            )
        else:
            error_code = result.get('error_code', 5000)
            status_code = status.HTTP_404_NOT_FOUND if error_code == 3002 else status.HTTP_500_INTERNAL_SERVER_ERROR
            
            return APIResponse.error(
                error_code=error_code,
                language=language,
                status_code=status_code
            )
        
    except Exception as e:
        logger.error(f"Error in increment consultant member view: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Get Member Favorite Status",
    description="หาสถานะการถูกใจเดิมของสมาชิกต่อที่ปรึกษาในโครงการ",
    parameters=[
        OpenApiParameter(
            name="app_project_id",
            type=str,
            location=OpenApiParameter.PATH,
            description="ID ของโครงการที่เลือก",
            required=True
        ),
        OpenApiParameter(
            name="user_consult_id",
            type=str,
            location=OpenApiParameter.PATH,
            description="ID ของที่ปรึกษาที่เลือก",
            required=True
        )
    ],
    responses={
        200: {
            "description": "สำเร็จ",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "error_message": None,
                        "error_code": None,
                        "data": {
                            "id": 123,
                            "app_project_id": 456,
                            "user_consult_id": 789,
                            "app_member_id": 101,
                            "member_favorite": "1",
                            "is_favorite": True
                        },
                        "api_version": "v.0.0.1"
                    }
                }
            }
        },
        404: {
            "description": "ไม่พบข้อมูล",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_message": "Data not found",
                        "error_code": 3002,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        }
    },
    tags=["Project Member Favorite"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_project_member_favorite_status(request, app_project_id, user_consult_id):
    """
    Get current member favorite status for a project-consultant pair
    """
    try:
        # Check if user is authenticated and is a member
        if not request.user.is_authenticated:
            return Response({
                'success': False,
                'error_message': 'Unauthorized',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Get app_member_id from authenticated user
        app_member_id = None
        if hasattr(request.user, 'user_type') and request.user.user_type == 'member':
            app_member_id = request.user.id
        elif hasattr(request.user, 'app_member_id'):
            app_member_id = request.user.app_member_id
        else:
            # Try to get from user's related member record
            try:
                from authentication.models import TcdAppMember
                member = TcdAppMember.objects.filter(user_id=request.user.id).first()
                if member:
                    app_member_id = member.id
            except:
                pass
        
        if not app_member_id:
            return Response({
                'success': False,
                'error_message': 'Unauthorized',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        result = ProjectMemberFavoriteService.get_member_favorite_status(
            app_project_id, user_consult_id, app_member_id
        )
        
        if result['success']:
            return Response({
                'success': True,
                'error_message': None,
                'error_code': None,
                'data': result['data'],
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error_message': result['message'],
                'error_code': result['error_code'],
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_404_NOT_FOUND)
            
    except Exception as e:
        logger.error(f"Error getting project member favorite status: {str(e)}")
        return Response({
            'success': False,
            'error_message': 'System error',
            'error_code': 5000,
            'data': {},
            'api_version': 'v.0.0.1'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Update Member Favorite Status",
    description="อัปเดตสถานะการถูกใจของสมาชิกต่อที่ปรึกษาในโครงการ (Toggle: ถ้าเดิม = '0' เปลี่ยนเป็น '1', ถ้าไม่ใช่ เปลี่ยนเป็น '0')",
    request=ProjectMemberFavoriteRequestSerializer,
    responses={
        200: ProjectMemberFavoriteUpdateResponseSerializer,
        400: {
            "description": "ข้อมูลที่ส่งมาไม่ถูกต้อง",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_message": "ข้อมูลที่ส่งมาไม่ถูกต้อง",
                        "error_code": 2000,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        },
        404: {
            "description": "ไม่พบข้อมูล",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_message": "Data not found",
                        "error_code": 3002,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        }
    },
    examples=[
        OpenApiExample(
            'Toggle Favorite Status',
            summary='อัปเดตสถานะการถูกใจ',
            description='Toggle สถานะการถูกใจของสมาชิกต่อที่ปรึกษาในโครงการ',
            value={
                "app_project_id": 123,
                "user_consult_id": 456
            }
        )
    ],
    tags=["Project Member Favorite"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_project_member_favorite_status(request):
    """
    Toggle member favorite status (0 -> 1, other -> 0)
    """
    language = get_language_from_request(request)
    
    try:
        # Validate request data
        serializer = ProjectMemberFavoriteRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Project member favorite validation failed: {serializer.errors}")
            return Response({
                'success': False,
                'error_message': 'ข้อมูลที่ส่งมาไม่ถูกต้อง',
                'error_code': 2000,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get parameters from validated data
        validated_data = serializer.validated_data
        app_project_id = validated_data['app_project_id']
        user_consult_id = validated_data['user_consult_id']
        
        # Check if user is authenticated and is a member
        if not request.user.is_authenticated:
            return Response({
                'success': False,
                'error_message': 'Unauthorized',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Get app_member_id from authenticated user
        app_member_id = None
        if hasattr(request.user, 'user_type') and request.user.user_type == 'member':
            app_member_id = request.user.id
        elif hasattr(request.user, 'app_member_id'):
            app_member_id = request.user.app_member_id
        else:
            # Try to get from user's related member record
            try:
                from authentication.models import TcdAppMember
                member = TcdAppMember.objects.filter(user_id=request.user.id).first()
                if member:
                    app_member_id = member.id
            except:
                pass
        
        if not app_member_id:
            return Response({
                'success': False,
                'error_message': 'Unauthorized',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        result = ProjectMemberFavoriteService.update_member_favorite_status(
            app_project_id, user_consult_id, app_member_id, request
        )
        
        if result['success']:
            return Response({
                'success': True,
                'error_message': None,
                'error_code': None,
                'data': result['data'],
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error_message': result['message'],
                'error_code': result['error_code'],
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_404_NOT_FOUND)
            
    except Exception as e:
        logger.error(f"Error updating project member favorite status: {str(e)}")
        return Response({
            'success': False,
            'error_message': 'System error',
            'error_code': 5000,
            'data': {},
            'api_version': 'v.0.0.1'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    tags=["Consultant Suitable Projects"],
    summary="Get suitable projects for consultant",
    description="ดึงรายการโครงการที่เหมาะสมสำหรับที่ปรึกษา โดยใช้เงื่อนไข matching >= project.result และโครงการที่ยังเปิดรับสมัคร",
    request=ConsultantSuitableProjectsRequestSerializer,
    responses={
        200: ConsultantSuitableProjectsResponseSerializer,
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่ได้รับอนุญาต"},
                "error_code": {"type": "integer", "example": 4001}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    parameters=[
        OpenApiParameter(
            name='page',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Page number for pagination (default: 1)'
        ),
        OpenApiParameter(
            name='page_size',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Number of items per page (default: 10, max: 100)'
        )
    ],
    examples=[
        OpenApiExample(
            'Basic Request',
            summary='ดึงรายการโครงการที่เหมาะสมพื้นฐาน',
            description='ดึงรายการโครงการที่เหมาะสมโดยไม่มีการกรอง',
            value={}
        ),
        OpenApiExample(
            'Sector Filter Request',
            summary='กรองตามสาขาเฉพาะ',
            description='ดึงรายการโครงการที่เหมาะสมในสาขาที่กำหนด',
            value={
                "sector_id": 1
            }
        ),
        OpenApiExample(
            'Sort by Matching Request',
            summary='เรียงตามผลการจับคู่',
            description='เรียงรายการโครงการตามผลการจับคู่จากมากไปน้อย',
            value={
                "sort_by_matching": True
            }
        ),
        OpenApiExample(
            'Combined Filter Request',
            summary='กรองและเรียงแบบรวม',
            description='กรองตามสาขาและเรียงตามผลการจับคู่',
            value={
                "sector_id": 2,
                "sort_by_matching": True
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_consultant_suitable_projects(request):
    """
    Get suitable projects for authenticated consultant
    """
    language = get_language_from_request(request)
    
    try:
        # Check if user is authenticated and is a consultant
        if not request.user.is_authenticated:
            return APIResponse.error(
                error_code=4001,
                language=language,
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        
        # Get user_consult_id from authenticated user
        user_consult_id = None
        
        # Check different ways to get consultant ID
        if hasattr(request.user, 'user_type') and request.user.user_type in ['consultant', 'consult']:
            user_consult_id = request.user.id
        elif hasattr(request.user, 'user_consult_id'):
            user_consult_id = request.user.user_consult_id
        else:
            # For JWT tokens, the user ID might directly be the consultant ID
            user_consult_id = request.user.id
        
        if not user_consult_id:
            return APIResponse.error(
                error_code=4001,
                language=language,
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        
        # Validate request data
        serializer = ConsultantSuitableProjectsRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Consultant suitable projects validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Get parameters from validated data
        validated_data = serializer.validated_data
        sector_id = validated_data.get('sector_id')
        sort_by_matching = validated_data.get('sort_by_matching', False)
        
        # Get pagination parameters from query string with validation
        try:
            page = int(request.GET.get('page', 1))
            if page <= 0:
                page = 1
        except (ValueError, TypeError):
            page = 1
            
        try:
            page_size = int(request.GET.get('page_size', 10))
            if page_size <= 0:
                page_size = 10
            elif page_size > 100:
                page_size = 100
        except (ValueError, TypeError):
            page_size = 10
        
        # Use ConsultantSuitableProjectsService to get suitable projects
        result = ConsultantSuitableProjectsService.get_suitable_projects(
            user_consult_id=user_consult_id,
            sector_id=sector_id,
            sort_by_matching=sort_by_matching,
            page=page,
            page_size=page_size,
            language=language
        )
        
        if result['success']:
            # Return response with pagination fields at root level
            from rest_framework.response import Response
            response_data = {
                'success': True,
                'error_code': None,
                'error_message': None,
                'data': result['data']['results'],
                'page': result['data']['pagination']['page'],
                'per_page': result['data']['pagination']['page_size'],
                'total': result['data']['pagination']['total_count'],
                'has_next': result['data']['pagination']['has_next'],
                'api_version': 'v.0.0.1'
            }
            return Response(response_data, status=status.HTTP_200_OK)
        else:
            error_code = result.get('error_code', 5000)
            return APIResponse.error(
                error_code=error_code,
                language=language,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
    except Exception as e:
        logger.error(f"Error in get consultant suitable projects: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    tags=["Consultant Favorite Projects"],
    summary="Get favorite projects for consultant",
    description="ดึงรายการโครงการที่ชอบสำหรับที่ปรึกษา (consult_favorite = '1') พร้อม pagination และตัวเลือกการเรียงลำดับ",
    request=ConsultantFavoriteProjectsRequestSerializer,
    responses={
        200: ConsultantFavoriteProjectsResponseSerializer,
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "Unauthorized"},
                "error_code": {"type": "integer", "example": 4001}
            }
        },
        500: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์"},
                "error_code": {"type": "integer", "example": 5000}
            }
        }
    },
    parameters=[
        OpenApiParameter(
            name='page',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Page number for pagination (default: 1)'
        ),
        OpenApiParameter(
            name='page_size',
            type=int,
            location=OpenApiParameter.QUERY,
            required=False,
            description='Number of items per page (default: 10, max: 100)'
        )
    ],
    examples=[
        OpenApiExample(
            'Basic Request',
            summary='ดึงรายการโครงการที่ชอบพื้นฐาน',
            description='ดึงรายการโครงการที่ชอบของที่ปรึกษาโดยไม่มีการกรอง',
            value={}
        ),
        OpenApiExample(
            'Sort by Matching Request',
            summary='ดึงรายการโครงการที่ชอบเรียงตามผลการจับคู่',
            description='ดึงรายการโครงการที่ชอบของที่ปรึกษาเรียงลำดับตามผลการจับคู่จากมากไปน้อย',
            value={
                "sort_by_matching": True
            }
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_consultant_favorite_projects(request):
    """
    Get list of favorite projects for consultant with pagination
    """
    language = get_language_from_request(request)
    
    try:
        # Check if user is authenticated and is a consultant
        if not request.user.is_authenticated:
            return Response({
                'success': False,
                'error_message': 'Unauthorized',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Get user_consult_id from authenticated user
        user_consult_id = None
        if hasattr(request.user, 'user_type') and request.user.user_type == 'consultant':
            user_consult_id = request.user.id
        else:
            return Response({
                'success': False,
                'error_message': 'Unauthorized - Only consultants can access this endpoint',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Validate request data
        serializer = ConsultantFavoriteProjectsRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Consultant favorite projects validation failed: {serializer.errors}")
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Get sorting parameter from validated data
        sort_by_matching = serializer.validated_data.get('sort_by_matching', False)
        
        # Get pagination parameters from query string with validation
        try:
            page = int(request.GET.get('page', 1))
            if page <= 0:
                page = 1
        except (ValueError, TypeError):
            page = 1
            
        try:
            page_size = int(request.GET.get('page_size', 10))
            if page_size <= 0:
                page_size = 10
            elif page_size > 100:
                page_size = 100
        except (ValueError, TypeError):
            page_size = 10
        
        # Use ConsultantFavoriteProjectsService to get favorite projects
        result = ConsultantFavoriteProjectsService.get_consultant_favorite_projects(
            user_consult_id=user_consult_id,
            sort_by_matching=sort_by_matching,
            page=page,
            page_size=page_size,
            language=language
        )
        
        if result['success']:
            # Return response with pagination fields at root level
            from rest_framework.response import Response
            response_data = {
                'success': True,
                'error_code': None,
                'error_message': None,
                'data': {
                    'results': result['data']['results']
                },
                'page': result['data']['pagination']['page'],
                'per_page': result['data']['pagination']['page_size'],
                'total': result['data']['pagination']['total_count'],
                'has_next': result['data']['pagination']['has_next'],
                'api_version': 'v.0.0.1'
            }
            return Response(response_data, status=status.HTTP_200_OK)
        else:
            error_code = result.get('error_code', 5000)
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            
            return APIResponse.error(
                error_code=error_code,
                language=language,
                status_code=status_code
            )
        
    except Exception as e:
        logger.error(f"Error in get consultant favorite projects: {str(e)}")
        return APIResponse.error(
            error_code=5000,
            language=language,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@extend_schema(
    summary="Get Consultant Favorite Status",
    description="หาสถานะการถูกใจเดิมของที่ปรึกษาต่อโครงการ (ดึง user_consult_id จาก JWT token)",
    parameters=[
        OpenApiParameter(
            name="app_project_id",
            type=str,
            location=OpenApiParameter.PATH,
            description="ID ของโครงการที่เลือก",
            required=True
        )
    ],
    responses={
        200: {
            "description": "สำเร็จ",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "error_message": None,
                        "error_code": None,
                        "data": {
                            "id": 123,
                            "app_project_id": 456,
                            "user_consult_id": 789,
                            "consult_favorite": "1",
                            "is_favorite": True
                        },
                        "api_version": "v.0.0.1"
                    }
                }
            }
        },
        401: {
            "description": "ไม่ได้รับอนุญาต",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_message": "Unauthorized",
                        "error_code": 4001,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        },
        404: {
            "description": "ไม่พบข้อมูล",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_message": "Data not found",
                        "error_code": 3002,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        }
    },
    tags=["Consultant Favorite"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_consultant_favorite_status(request, app_project_id):
    """
    Get current consultant favorite status for a project-consultant pair
    """
    try:
        # Check if user is authenticated and is a consultant
        if not request.user.is_authenticated:
            return Response({
                'success': False,
                'error_message': 'Unauthorized',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Get user_consult_id from JWT token
        user_consult_id = None
        if hasattr(request.user, 'user_type') and request.user.user_type in ['consultant', 'consult']:
            user_consult_id = request.user.id
        elif hasattr(request.user, 'user_consult_id'):
            user_consult_id = request.user.user_consult_id
        else:
            # For JWT tokens, the user ID might directly be the consultant ID
            user_consult_id = request.user.id
        
        if not user_consult_id:
            return Response({
                'success': False,
                'error_message': 'Unauthorized - Consultant ID not found in token',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        result = ConsultantFavoriteService.get_consultant_favorite_status(
            app_project_id, user_consult_id
        )
        
        if result['success']:
            return Response({
                'success': True,
                'error_message': None,
                'error_code': None,
                'data': result['data'],
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error_message': result['message'],
                'error_code': result['error_code'],
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_404_NOT_FOUND)
            
    except Exception as e:
        logger.error(f"Error getting consultant favorite status: {str(e)}")
        return Response({
            'success': False,
            'error_message': 'System error',
            'error_code': 5000,
            'data': {},
            'api_version': 'v.0.0.1'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@extend_schema(
    summary="Update Consultant Favorite Status",
    description="อัปเดตสถานะการถูกใจของที่ปรึกษาต่อโครงการ (Toggle: ถ้าเดิม = '0' เปลี่ยนเป็น '1', ถ้าไม่ใช่ เปลี่ยนเป็น '0') (ดึง user_consult_id จาก JWT token)",
    parameters=[
        OpenApiParameter(
            name="app_project_id",
            type=str,
            location=OpenApiParameter.PATH,
            description="ID ของโครงการที่เลือก",
            required=True
        )
    ],
    responses={
        200: ConsultantFavoriteUpdateResponseSerializer,
        401: {
            "description": "ไม่ได้รับอนุญาต",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_message": "Unauthorized",
                        "error_code": 4001,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        },
        404: {
            "description": "ไม่พบข้อมูล",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "error_message": "Data not found",
                        "error_code": 3002,
                        "data": {},
                        "api_version": "v.0.0.1"
                    }
                }
            }
        }
    },
    tags=["Consultant Favorite"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_consultant_favorite_status(request, app_project_id):
    """
    Toggle consultant favorite status (0 -> 1, other -> 0)
    """
    try:
        # Check if user is authenticated and is a consultant
        if not request.user.is_authenticated:
            return Response({
                'success': False,
                'error_message': 'Unauthorized',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Get user_consult_id from JWT token
        user_consult_id = None
        if hasattr(request.user, 'user_type') and request.user.user_type in ['consultant', 'consult']:
            user_consult_id = request.user.id
        elif hasattr(request.user, 'user_consult_id'):
            user_consult_id = request.user.user_consult_id
        else:
            # For JWT tokens, the user ID might directly be the consultant ID
            user_consult_id = request.user.id
        
        if not user_consult_id:
            return Response({
                'success': False,
                'error_message': 'Unauthorized - Consultant ID not found in token',
                'error_code': 4001,
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        result = ConsultantFavoriteService.update_consultant_favorite_status(
            app_project_id, user_consult_id, request
        )
        
        if result['success']:
            return Response({
                'success': True,
                'error_message': None,
                'error_code': None,
                'data': result['data'],
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error_message': result['message'],
                'error_code': result['error_code'],
                'data': {},
                'api_version': 'v.0.0.1'
            }, status=status.HTTP_404_NOT_FOUND)
            
    except Exception as e:
        logger.error(f"Error updating consultant favorite status: {str(e)}")
        return Response({
            'success': False,
            'error_message': 'System error',
            'error_code': 5000,
            'data': {},
            'api_version': 'v.0.0.1'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

